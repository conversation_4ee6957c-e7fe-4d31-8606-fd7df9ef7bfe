from flask import Flask, render_template, request, jsonify, url_for
import os
from datetime import datetime
from pymongo import MongoClient
import cloudinary
import cloudinary.uploader
from dotenv import load_dotenv

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'static/uploads'


@app.route('/')
def index():
    # Current implementation
    properties = list(properties_collection.find().sort('created_at', -1))
    # Add error handling and fallback
    try:
        properties = list(properties_collection.find().sort('created_at', -1))
        if not properties:
            print("No properties found in database")
    except Exception as e:
        print(f"Database error: {e}")
        properties = []
    for prop in properties:
        prop['_id'] = str(prop['_id'])
    return render_template('index.html', properties=properties)

@app.route('/post-property')
def post_property():
    """Property posting page"""
    return render_template('post_property.html')

@app.route('/api/search', methods=['POST'])
def search_properties():
    """Search properties based on filters"""
    data = request.get_json()
    
    city = data.get('city', '').lower()
    keyword = data.get('keyword', '').lower()
    price_range = data.get('price_range', '')
    
    filtered_properties = sample_properties.copy()
    
    # Filter by city
    if city:
        filtered_properties = [p for p in filtered_properties 
                             if city in p['location'].lower()]
    
    # Filter by keyword
    if keyword:
        filtered_properties = [p for p in filtered_properties 
                             if keyword in p['title'].lower() or 
                                keyword in p['description'].lower()]
    
    # Filter by price range
    if price_range:
        min_price, max_price = map(int, price_range.split('-'))
        filtered_properties = [p for p in filtered_properties 
                             if min_price <= p['price'] <= max_price]
    
    return jsonify({
        'success': True,
        'properties': filtered_properties,
        'count': len(filtered_properties)
    })

# MongoDB configuration
import os
from dotenv import load_dotenv
load_dotenv()

# MongoDB Atlas configuration
# MongoDB configuration
client = MongoClient(os.getenv('MONGO_URI'))
mongo_db = client['eznest']  # Updated database name
properties_collection = mongo_db['properties']  # Existing collection name

# Cloudinary config
cloudinary.config(
  cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
  api_key=os.getenv('CLOUDINARY_API_KEY'),
  api_secret=os.getenv('CLOUDINARY_API_SECRET')
)

@app.route('/api/properties', methods=['POST'])
def create_property():
    try:
        # Existing form data collection
        
        # Handle file uploads
        uploaded_files = []
        for key in request.files:
            if key.startswith('image_'):
                file = request.files[key]
                ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

                def allowed_file(filename):
                    return '.' in filename and \
                           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

                # In create_property route:
                if file and allowed_file(file.filename):
                    # upload logic
                    upload_result = cloudinary.uploader.upload(file)
                    uploaded_files.append(upload_result['url'])

        # Create property object
        new_property = {
            'title': title,
            'location': f"{address}, {city}",
            'price': price,
            'rent_frequency': rent_frequency,
            'type': property_type,
            'description': description,
            'image_url': uploaded_files[0] if uploaded_files else '/static/images/placeholder.jpg',
            'phone': phone,
            'upvotes': 0,
            'downvotes': 0,
            'created_at': datetime.now()
        }
        
        result = properties_collection.insert_one(new_property)

        return jsonify({
            'success': True,
            'message': 'Property posted successfully',
            'property_id': str(result.inserted_id)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 400

# Update other routes to use MongoDB as needed

if __name__ == '__main__':
    app.run(debug=True)
