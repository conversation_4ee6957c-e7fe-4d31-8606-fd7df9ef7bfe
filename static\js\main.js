// Global variables
let currentProperties = []
const flippedCards = new Set()

// Initialize application
document.addEventListener("DOMContentLoaded", () => {
  initializeApp()
})

function initializeApp() {
  // Get initial properties data
  const propertyCards = document.querySelectorAll(".property-card")
  currentProperties = Array.from(propertyCards).map((card) => ({
    id: Number.parseInt(card.dataset.propertyId),
    price: Number.parseInt(card.dataset.price),
    type: card.dataset.type,
    element: card,
  }))

  // Initialize event listeners
  initializeSearch()
  initializeImageUpload()
  initializeForm()
}

// Search functionality
function initializeSearch() {
  const searchForm = document.getElementById("searchForm")
  if (searchForm) {
    searchForm.addEventListener("submit", handleSearch)
  }
}

function handleSearch(e) {
  e.preventDefault()

  const city = document.getElementById("cityInput").value.toLowerCase()
  const keyword = document.getElementById("keywordInput").value.toLowerCase()
  const priceRange = document.getElementById("priceRange").value

  const searchData = {
    city: city,
    keyword: keyword,
    price_range: priceRange,
  }

  fetch("/api/search", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(searchData),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        updateResultsCount(data.count)
        // In a real implementation, you would update the grid with new properties
      }
    })
    .catch((error) => {
      console.error("Search error:", error)
    })
}

function updateResultsCount(count) {
  const resultsCount = document.getElementById("resultsCount")
  if (resultsCount) {
    resultsCount.textContent = `${count} Properties Found`
  }
}

// Card flip functionality
function flipCard(button) {
  const card = button.closest(".property-card")
  const propertyId = Number.parseInt(card.dataset.propertyId)

  if (flippedCards.has(propertyId)) {
    flippedCards.delete(propertyId)
    card.classList.remove("flipped")
  } else {
    flippedCards.add(propertyId)
    card.classList.add("flipped")
  }
}

// WhatsApp chat functionality
function chatWhatsApp(phone, title) {
  const message = encodeURIComponent(`Hi! I'm interested in your property: ${title}`)
  const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, "")}?text=${message}`
  window.open(whatsappUrl, "_blank")
}

// Voting functionality
function vote(propertyId, voteType) {
  const voteData = {
    property_id: propertyId,
    vote_type: voteType,
  }

  fetch("/api/vote", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(voteData),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        updateVoteCount(propertyId, voteType, data.new_count)
      }
    })
    .catch((error) => {
      console.error("Vote error:", error)
    })
}

function updateVoteCount(propertyId, voteType, newCount) {
  const card = document.querySelector(`[data-property-id="${propertyId}"]`)
  if (card) {
    const voteButton = card.querySelector(`.${voteType}vote span`)
    if (voteButton) {
      voteButton.textContent = newCount
    }
  }
}

// Report functionality
function reportProperty(propertyId) {
  if (confirm("Are you sure you want to report this property?")) {
    const reportData = {
      property_id: propertyId,
      reason: "User reported",
    }

    fetch("/api/report", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(reportData),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert("Property reported successfully. Thank you for helping us maintain quality.")
        }
      })
      .catch((error) => {
        console.error("Report error:", error)
        alert("Error reporting property. Please try again.")
      })
  }
}

// Image upload functionality
function initializeImageUpload() {
  const uploadArea = document.getElementById("imageUploadArea")
  const imageInput = document.getElementById("imageInput")

  if (!uploadArea || !imageInput) return

  // Click to upload
  uploadArea.addEventListener("click", () => imageInput.click())

  // Drag and drop
  uploadArea.addEventListener("dragover", handleDragOver)
  uploadArea.addEventListener("dragleave", handleDragLeave)
  uploadArea.addEventListener("drop", handleDrop)

  // File input change
  imageInput.addEventListener("change", handleFileSelect)
}

function handleDragOver(e) {
  e.preventDefault()
  e.currentTarget.classList.add("dragover")
}

function handleDragLeave(e) {
  e.preventDefault()
  e.currentTarget.classList.remove("dragover")
}

function handleDrop(e) {
  e.preventDefault()
  e.currentTarget.classList.remove("dragover")

  const files = e.dataTransfer.files
  if (files.length > 0) {
    handleFileSelect({ target: { files: files } })
  }
}

function handleFileSelect(e) {
  const file = e.target.files[0]
  if (file && file.type.startsWith("image/")) {
    const reader = new FileReader()
    reader.onload = (e) => {
      showImagePreview(e.target.result)
    }
    reader.readAsDataURL(file)
  }
}

function showImagePreview(src) {
  const uploadArea = document.getElementById("imageUploadArea")
  const imagePreview = document.getElementById("imagePreview")
  const previewImg = document.getElementById("previewImg")

  uploadArea.style.display = "none"
  previewImg.src = src
  imagePreview.style.display = "block"
}

function removeImage() {
  const uploadArea = document.getElementById("imageUploadArea")
  const imagePreview = document.getElementById("imagePreview")
  const imageInput = document.getElementById("imageInput")

  uploadArea.style.display = "block"
  imagePreview.style.display = "none"
  imageInput.value = ""
}

// Form functionality
function initializeForm() {
  const propertyForm = document.getElementById("propertyForm")
  if (propertyForm) {
    propertyForm.addEventListener("submit", handleFormSubmit)
  }
}

function handleFormSubmit(e) {
  e.preventDefault()

  const submitBtn = document.getElementById("submitBtn")
  const btnText = submitBtn.querySelector(".btn-text")
  const btnLoading = submitBtn.querySelector(".btn-loading")

  // Show loading state
  btnText.style.display = "none"
  btnLoading.style.display = "inline-flex"
  submitBtn.disabled = true

  const formData = new FormData(e.target)

  // Add image if selected
  const imageInput = document.getElementById("imageInput")
  if (imageInput.files.length > 0) {
    formData.append("image", imageInput.files[0])
  }

  fetch("/api/properties", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert("Property posted successfully!")
        resetForm()
      } else {
        throw new Error(data.message || "Failed to post property")
      }
    })
    .catch((error) => {
      console.error("Form submission error:", error)
      alert("Error posting property. Please try again.")
    })
    .finally(() => {
      // Reset loading state
      btnText.style.display = "inline"
      btnLoading.style.display = "none"
      submitBtn.disabled = false
    })
}

function resetForm() {
  const form = document.getElementById("propertyForm")
  if (form) {
    form.reset()
    removeImage()
  }
}

// Export functions for global access
window.flipCard = flipCard
window.chatWhatsApp = chatWhatsApp
window.vote = vote
window.reportProperty = reportProperty
window.removeImage = removeImage
window.resetForm = resetForm
