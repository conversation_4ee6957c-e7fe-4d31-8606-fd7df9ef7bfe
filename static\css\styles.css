/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, Helvetica, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* Utility Classes */
.min-h-screen {
  min-height: 100vh;
}
.bg-gray-50 {
  background-color: #f9fafb;
}
.bg-white {
  background-color: white;
}
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.border-b {
  border-bottom: 1px solid #e5e7eb;
}
.max-w-7xl {
  max-width: 80rem;
  margin: 0 auto;
}
.max-w-4xl {
  max-width: 56rem;
  margin: 0 auto;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mr-4 {
  margin-right: 1rem;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: 1rem;
}
.relative {
  position: relative;
}
.full-width {
  width: 100%;
}

/* Typography */
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.font-bold {
  font-weight: 700;
}
.font-semibold {
  font-weight: 600;
}
.text-gray-900 {
  color: #111827;
}
.text-gray-600 {
  color: #4b5563;
}

/* Grid System */
.grid {
  display: grid;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Search Input Styles */
.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  background: white;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.search-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
  color: #6b7280;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-decoration: none;
  gap: 0.5rem;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-whatsapp {
  background-color: #25d366;
  color: white;
}

.btn-whatsapp:hover {
  background-color: #128c7e;
}

.btn-icon {
  font-size: 1rem;
}

/* Property Card Styles */
.property-card {
  perspective: 1000px;
  height: 400px;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.property-card.flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background: white;
  overflow: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

.property-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.report-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.15s ease-in-out;
}

.report-btn:hover {
  background: white;
}

.card-content {
  padding: 1.5rem;
  text-align: left;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.property-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
  line-height: 1.4;
}

.property-location {
  color: #6b7280;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.property-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 1rem;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Card Back Styles */
.card-back-content {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-back-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-back-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
}

.card-back-body {
  flex: 1;
  overflow-y: auto;
  text-align: left;
}

.property-description {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.5;
  max-height: 100px;
  overflow-y: auto;
}

.property-details p {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.card-back-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.voting-section {
  margin-bottom: 1rem;
}

.vote-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.vote-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.vote-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  font-size: 0.875rem;
}

.vote-btn:hover {
  background: #e5e7eb;
}

.vote-btn.upvote:hover {
  background: #dcfce7;
  border-color: #16a34a;
}

.vote-btn.downvote:hover {
  background: #fee2e2;
  border-color: #dc2626;
}

/* Form Styles */
.form-card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.property-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Image Upload Styles */
.image-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  transition: border-color 0.15s ease-in-out;
  cursor: pointer;
}

.image-upload-area:hover {
  border-color: #2563eb;
}

.image-upload-area.dragover {
  border-color: #2563eb;
  background: #eff6ff;
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.upload-link {
  color: #2563eb;
  text-decoration: underline;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.image-preview {
  position: relative;
  margin-top: 1rem;
}

.image-preview img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 0.5rem;
}

.remove-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(220, 38, 38, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  cursor: pointer;
  font-size: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.integration-info {
  background: #eff6ff;
  padding: 1.5rem;
  margin-top: 2rem;
}

.integration-info h3 {
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.integration-info code {
  background: #dbeafe;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

/* No Properties */
.no-properties {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.no-properties h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #6b7280;
}

.no-properties a {
  color: #2563eb;
  text-decoration: none;
}

/* Loading States */
.btn-loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-loading::after {
  content: "";
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (min-width: 768px) {
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .properties-grid {
    grid-template-columns: 1fr;
  }

  .md\:grid-cols-4 {
    grid-template-columns: 1fr;
  }
}
