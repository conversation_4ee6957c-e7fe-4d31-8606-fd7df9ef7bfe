<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block meta_description %}HavenHuts - Find your perfect room rental in India. Browse apartments, flats, PG accommodations and more.{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}room rental, apartment, flat, PG, accommodation, India, rent{% endblock %}">
    <meta name="author" content="HavenHuts">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}HavenHuts - Room Rental Platform{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Find your perfect room rental in India{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:image" content="{{ url_for('static', filename='images/og-image.jpg') }}">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}HavenHuts - Room Rental Platform{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}Find your perfect room rental in India{% endblock %}">
    
    <title>{% block title %}HavenHuts - Find Your Perfect Room{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    
    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "HavenHuts",
        "url": "{{ request.url_root }}",
        "description": "Room rental platform in India",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.url_root }}search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="container">
                <div class="nav-content">
                    <a href="{{ url_for('index') }}" class="logo">
                        <h1>HavenHuts</h1>
                    </a>
                    <div class="nav-links">
                        <a href="{{ url_for('index') }}" class="nav-link">Home</a>
                        <a href="{{ url_for('post_property') }}" class="nav-link">Post Property</a>
                        <a href="#" class="nav-link">About</a>
                    </div>
                    <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <main class="main">
        {% block content %}{% endblock %}
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>HavenHuts</h3>
                    <p>Find your perfect room rental in India</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="{{ url_for('index') }}">Home</a></li>
                        <li><a href="{{ url_for('post_property') }}">Post Property</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 HavenHuts. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
