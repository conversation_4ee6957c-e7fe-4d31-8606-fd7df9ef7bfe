{% extends "base.html" %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900">HavenHuts</h1>
                <a href="{{ url_for('post_property') }}" class="btn btn-primary">Post Property</a>
            </div>
        </div>
    </header>

    <!-- Search Section -->
    <section class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <form id="searchForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="relative">
                    <span class="search-icon">🔍</span>
                    <input type="text" id="keywordInput" placeholder="Search keywords..." class="search-input">
                </div>
                <div class="relative">
                    <span class="search-icon">📍</span>
                    <input type="text" id="cityInput" placeholder="City..." class="search-input">
                </div>
                <div class="relative">
                    <span class="search-icon">₹</span>
                    <select id="priceRange" class="search-input">
                        <option value="">Any Price</option>
                        <option value="0-5000">₹0 - ₹5,000</option>
                        <option value="5000-10000">₹5,000 - ₹10,000</option>
                        <option value="10000-20000">₹10,000 - ₹20,000</option>
                        <option value="20000-50000">₹20,000 - ₹50,000</option>
                        <option value="50000-999999">₹50,000+</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <span class="btn-icon">🔍</span>
                    Search
                </button>
            </form>
        </div>
    </section>

    <!-- Properties Grid -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900" id="resultsCount">
                {{ properties|length if properties else 0 }} Properties Found
            </h2>
        </div>

        <div class="properties-grid" id="propertiesGrid">
            {% if properties %}
                {% for property in properties %}
                <div class="property-card" data-property-id="{{ property.id }}" data-price="{{ property.price }}" data-type="{{ property.type }}">
                    <div class="card-inner">
                        <!-- Front of Card -->
                        <div class="card-front">
                            <div class="relative">
                                <img src="{{ property.image_url or '/static/images/placeholder.jpg' }}" 
                                     alt="{{ property.title }}" class="property-image">
                                <button class="report-btn" onclick="reportProperty({{ property.id }})" title="Report">
                                    🚩
                                </button>
                            </div>

                            <div class="card-content">
                                <h3 class="property-title">{{ property.title }}</h3>
                                <p class="property-location">
                                    📍 {{ property.location }}
                                </p>
                                <p class="property-price">₹{{ "{:,}".format(property.price) }}/{{ property.rent_frequency }}</p>

                                <div class="card-actions">
                                    <button onclick="flipCard(this)" class="btn btn-primary full-width">
                                        View Details
                                    </button>
                                    <button onclick="chatWhatsApp('{{ property.phone }}', '{{ property.title }}')" 
                                            class="btn btn-whatsapp full-width">
                                        💬 Chat on WhatsApp
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Back of Card -->
                        <div class="card-back">
                            <div class="card-back-content">
                                <div class="card-back-header">
                                    <h3>Property Details</h3>
                                    <button onclick="flipCard(this)" class="close-btn">✕</button>
                                </div>

                                <div class="card-back-body">
                                    <p class="property-description">{{ property.description }}</p>

                                    <div class="property-details">
                                        <p><strong>Location:</strong> {{ property.location }}</p>
                                        <p><strong>Price:</strong> ₹{{ "{:,}".format(property.price) }}/{{ property.rent_frequency }}</p>
                                        <p><strong>Type:</strong> {{ property.type|title }}</p>
                                    </div>
                                </div>

                                <div class="card-back-footer">
                                    <div class="voting-section">
                                        <span class="vote-label">Community Rating</span>
                                        <div class="vote-buttons">
                                            <button onclick="vote({{ property.id }}, 'up')" class="vote-btn upvote">
                                                👍 <span>{{ property.upvotes or 0 }}</span>
                                            </button>
                                            <button onclick="vote({{ property.id }}, 'down')" class="vote-btn downvote">
                                                👎 <span>{{ property.downvotes or 0 }}</span>
                                            </button>
                                        </div>
                                    </div>

                                    <button onclick="chatWhatsApp('{{ property.phone }}', '{{ property.title }}')" 
                                            class="btn btn-whatsapp full-width">
                                        💬 Contact Owner
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-properties">
                    <h3>No properties found</h3>
                    <p>Try adjusting your search criteria or <a href="{{ url_for('post_property') }}">post a new property</a>.</p>
                </div>
            {% endif %}
        </div>
    </main>
</div>
{% endblock %}
