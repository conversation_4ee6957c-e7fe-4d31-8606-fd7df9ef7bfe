{% extends "base.html" %}

{% block meta_description %}Find the best room rentals in India. Browse apartments, flats, PG accommodations with prices in INR. Connect directly with property owners.{% endblock %}

{% block content %}
<!-- Search Section -->
<section class="search-section">
    <div class="container">
        <div class="search-container">
            <h2>Find Your Perfect Room</h2>
            <form class="search-form" id="searchForm">
                <div class="search-inputs">
                    <div class="input-group">
                        <input type="text" id="cityInput" placeholder="Enter city..." class="search-input">
                        <span class="input-icon">📍</span>
                    </div>
                    <div class="input-group">
                        <input type="text" id="keywordInput" placeholder="Keywords..." class="search-input">
                        <span class="input-icon">🔍</span>
                    </div>
                    <div class="input-group">
                        <select id="priceRange" class="search-input">
                            <option value="">Any Price</option>
                            <option value="0-5000">₹0 - ₹5,000</option>
                            <option value="5000-10000">₹5,000 - ₹10,000</option>
                            <option value="10000-20000">₹10,000 - ₹20,000</option>
                            <option value="20000-50000">₹20,000 - ₹50,000</option>
                            <option value="50000-999999">₹50,000+</option>
                        </select>
                    </div>
                    <button type="submit" class="search-btn">Search</button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section">
    <div class="container">
        <div class="filters-container">
            <div class="filter-group">
                <label for="sortBy">Sort by:</label>
                <select id="sortBy" class="filter-select">
                    <option value="recommended">Recommended</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="newest">Newest First</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="propertyType">Property Type:</label>
                <select id="propertyType" class="filter-select">
                    <option value="">All Types</option>
                    <option value="flat">Flat</option>
                    <option value="apartment">Apartment</option>
                    <option value="pg">PG</option>
                    <option value="land">Land</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="results-count">
                <span id="resultsCount">{{ properties|length if properties else 0 }} properties found</span>
            </div>
        </div>
    </div>
</section>

<!-- Properties Grid -->
<section class="properties-section">
    <div class="container">
        <div class="properties-grid" id="propertiesGrid">
            {% if properties %}
                {% for property in properties %}
                <div class="property-card" data-property-id="{{ property.id }}" data-price="{{ property.price }}" data-type="{{ property.type }}">
                    <div class="card-inner">
                        <!-- Front of card -->
                        <div class="card-front">
                            <div class="property-image">
                                <img src="{{ property.image_url or '/static/images/placeholder.jpg' }}" alt="{{ property.title }}" loading="lazy">
                                <div class="property-type-badge">{{ property.type|title }}</div>
                                <button class="report-btn" onclick="reportProperty({{ property.id }})" title="Report this property">
                                    🚩
                                </button>
                            </div>
                            <div class="property-info">
                                <h3 class="property-title">{{ property.title }}</h3>
                                <p class="property-location">📍 {{ property.location }}</p>
                                <div class="property-price">₹{{ "{:,}".format(property.price) }}/{{ property.rent_frequency }}</div>
                                <div class="property-actions">
                                    <button class="btn btn-primary view-details-btn" onclick="flipCard(this)">
                                        View Details
                                    </button>
                                    <button class="btn btn-whatsapp" onclick="chatWhatsApp('{{ property.phone }}', '{{ property.title }}')">
                                        💬 WhatsApp
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Back of card -->
                        <div class="card-back">
                            <div class="property-details">
                                <div class="details-header">
                                    <h3>{{ property.title }}</h3>
                                    <button class="close-btn" onclick="flipCard(this)">✕</button>
                                </div>
                                <div class="details-content">
                                    <div class="detail-item">
                                        <strong>Location:</strong> {{ property.location }}
                                    </div>
                                    <div class="detail-item">
                                        <strong>Price:</strong> ₹{{ "{:,}".format(property.price) }}/{{ property.rent_frequency }}
                                    </div>
                                    <div class="detail-item">
                                        <strong>Type:</strong> {{ property.type|title }}
                                    </div>
                                    <div class="detail-item description">
                                        <strong>Description:</strong>
                                        <div class="description-text">{{ property.description }}</div>
                                    </div>
                                </div>
                                <div class="voting-section">
                                    <div class="vote-buttons">
                                        <button class="vote-btn upvote" onclick="vote({{ property.id }}, 'up')">
                                            👍 <span class="vote-count">{{ property.upvotes or 0 }}</span>
                                        </button>
                                        <button class="vote-btn downvote" onclick="vote({{ property.id }}, 'down')">
                                            👎 <span class="vote-count">{{ property.downvotes or 0 }}</span>
                                        </button>
                                    </div>
                                </div>
                                <button class="btn btn-whatsapp full-width" onclick="chatWhatsApp('{{ property.phone }}', '{{ property.title }}')">
                                    Contact Owner via WhatsApp
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-results">
                    <h3>No properties found</h3>
                    <p>Be the first to post a property!</p>
                </div>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    initializeSearch();
});
</script>
{% endblock %}
