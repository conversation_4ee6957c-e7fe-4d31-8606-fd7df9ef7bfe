{% extends "base.html" %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center">
                <a href="{{ url_for('index') }}" class="btn btn-secondary mr-4">
                    ← Back to Listings
                </a>
                <h1 class="text-2xl font-bold text-gray-900">Post Your Property</h1>
            </div>
        </div>
    </header>

    <!-- Form -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="form-card">
            <form id="propertyForm" class="property-form" enctype="multipart/form-data">
                <!-- Image Upload -->
                <div class="form-group">
                    <label class="form-label">Property Photo *</label>
                    <div class="image-upload-area" id="imageUploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">📷</div>
                            <p>Drag and drop your photo here, or <span class="upload-link">click to browse</span></p>
                            <p class="upload-hint">PNG, JPG, GIF up to 10MB</p>
                        </div>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    </div>
                    <div id="imagePreview" class="image-preview" style="display: none;">
                        <img id="previewImg" src="/placeholder.svg" alt="Preview">
                        <button type="button" onclick="removeImage()" class="remove-btn">✕</button>
                    </div>
                </div>

                <!-- Property Details -->
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Property Title *</label>
                        <input type="text" name="title" placeholder="e.g., Cozy Studio Apartment" required class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Monthly Rent (₹) *</label>
                        <input type="number" name="price" placeholder="25000" required class="form-input">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Full Address *</label>
                    <input type="text" name="address" placeholder="123 Main Street, Apt 4B" required class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label">City *</label>
                    <input type="text" name="city" placeholder="Mumbai" required class="form-input">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Property Type *</label>
                        <select name="property_type" required class="form-input">
                            <option value="">Select Type</option>
                            <option value="flat">Flat</option>
                            <option value="apartment">Apartment</option>
                            <option value="pg">PG (Paying Guest)</option>
                            <option value="land">Land</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Rent Frequency *</label>
                        <select name="rent_frequency" required class="form-input">
                            <option value="">Select Frequency</option>
                            <option value="monthly">Monthly</option>
                            <option value="yearly">Yearly</option>
                            <option value="daily">Daily</option>
                            <option value="quarterly">Quarterly</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Property Description *</label>
                    <textarea name="description" rows="6" 
                              placeholder="Describe your property, amenities, nearby attractions, and any important details..." 
                              required class="form-input"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">WhatsApp Number *</label>
                    <input type="tel" name="phone" placeholder="+91 9876543210" required class="form-input">
                </div>

                <!-- Submit Button -->
                <div class="form-actions">
                    <button type="button" onclick="resetForm()" class="btn btn-secondary">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span class="btn-text">Post Property</span>
                        <span class="btn-loading" style="display: none;">Posting...</span>
                    </button>
                </div>
            </form>

            <!-- Backend Integration Info -->
            <div class="integration-info">
                <h3>Flask Backend Integration</h3>
                <p>This form submits to <code>/api/properties</code> endpoint. The FormData includes: title, address, city, price, description, and image file.</p>
            </div>
        </div>
    </main>
</div>
{% endblock %}
